# Phishing URL Detection System

A production-ready FastAPI web application that predicts whether a given URL is legitimate (0) or phishing (1) using advanced machine learning techniques. The system extracts 30 comprehensive features from URLs and uses a stacking ensemble classifier for accurate predictions.

## 🚀 Features

- **Real-time URL Analysis**: Fetches actual web content and analyzes 30 different features
- **Machine Learning Powered**: Uses a stacking ensemble classifier with multiple base models
- **Web Interface**: Professional HTML/CSS/JavaScript interface for easy interaction
- **REST API**: Comprehensive API with batch processing capabilities
- **Robust Error Handling**: Graceful handling of network errors, timeouts, and invalid URLs
- **Performance Monitoring**: Built-in metrics and logging for production use
- **Comprehensive Logging**: Detailed logging with rotation and error tracking

## 📋 Requirements

### System Requirements
- Python 3.8 or higher
- Internet connection (for fetching URL content)
- Minimum 4GB RAM (recommended 8GB)
- 1GB free disk space

### Python Dependencies
The application requires the following packages:
```
fastapi>=0.68.0
uvicorn[standard]>=0.15.0
pandas>=1.3.0
numpy>=1.21.0
scikit-learn>=1.0.0
requests>=2.25.0
beautifulsoup4>=4.9.0
tldextract>=3.1.0
category-encoders>=2.3.0
lightgbm>=3.2.0
xgboost>=1.4.0
joblib>=1.0.0
pydantic>=1.8.0
```

## 🛠️ Installation & Setup

### 1. Clone or Download the Project
```bash
# If using git
git clone <repository-url>
cd phishing-detection

# Or download and extract the files to a directory
```

### 2. Install Dependencies
```bash
# Install required packages
pip install fastapi uvicorn pandas numpy scikit-learn requests beautifulsoup4 tldextract category-encoders lightgbm xgboost joblib pydantic
```

### 3. Verify Model File
Ensure the `stacking_ensemble.joblib` file is present in the project directory. This file contains the trained model and preprocessors.

### 4. Run the Application
```bash
# Start the FastAPI server
python main.py

# Or use uvicorn directly
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

The application will be available at:
- **Web Interface**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Alternative API Docs**: http://localhost:8000/redoc

## 🌐 Web Interface Usage

1. **Open your browser** and navigate to http://localhost:8000
2. **Enter a URL** in the input field (e.g., `https://example.com` or `www.example.com`)
3. **Click "Analyze URL"** to start the analysis
4. **View Results** including:
   - Prediction (Legitimate/Phishing)
   - Confidence level
   - Detailed probability scores
   - Processing time

### Web Interface Features
- **Real-time Analysis**: Live processing with loading indicators
- **Responsive Design**: Works on desktop and mobile devices
- **Error Handling**: Clear error messages for invalid URLs or network issues
- **Technical Details**: Modal with detailed analysis information

## 🔌 API Usage

### Authentication
No authentication required for this version.

### Base URL
```
http://localhost:8000
```

### Endpoints

#### 1. Single URL Prediction
**POST** `/predict`

**Request Body:**
```json
{
    "url": "https://example.com"
}
```

**Response:**
```json
{
    "success": true,
    "url": "https://example.com",
    "prediction": 0,
    "prediction_label": "Legitimate",
    "confidence": 0.8542,
    "probabilities": {
        "legitimate": 0.8542,
        "phishing": 0.1458
    },
    "processing_time": 2.341,
    "timestamp": "2025-01-08 10:30:45"
}
```

#### 2. Batch URL Prediction
**POST** `/predict/batch`

**Request Body:**
```json
[
    "https://example.com",
    "https://suspicious-site.com",
    "www.google.com"
]
```

**Response:**
```json
{
    "results": [
        {
            "success": true,
            "url": "https://example.com",
            "prediction": 0,
            "prediction_label": "Legitimate",
            "confidence": 0.8542,
            "probabilities": {
                "legitimate": 0.8542,
                "phishing": 0.1458
            },
            "processing_time": 2.341,
            "timestamp": "2025-01-08 10:30:45"
        }
    ],
    "summary": {
        "total": 3,
        "successful": 2,
        "failed": 1
    }
}
```

#### 3. Health Check
**GET** `/health`

**Response:**
```json
{
    "status": "healthy",
    "timestamp": "2025-01-08 10:30:45",
    "components": {
        "feature_extractor": true,
        "preprocessor": true
    }
}
```

#### 4. Model Information
**GET** `/model/info`

**Response:**
```json
{
    "model_info": {
        "expected_features": ["LengthOfURL", "URLComplexity", ...],
        "num_features": 30,
        "categorical_columns": [],
        "numerical_columns": ["LengthOfURL", "URLComplexity", ...],
        "preprocessing_steps": [
            "1. Missing value imputation",
            "2. Target encoding for categorical features",
            "3. Variance threshold feature selection",
            "4. Standard scaling",
            "5. SelectKBest feature selection"
        ]
    }
}
```

#### 5. Performance Metrics
**GET** `/metrics`

**Response:**
```json
{
    "performance_metrics": {
        "requests_total": 150,
        "requests_successful": 142,
        "requests_failed": 8,
        "avg_response_time": 2.341
    },
    "timestamp": "2025-01-08 10:30:45"
}
```

### Error Responses

All endpoints return structured error responses:

```json
{
    "success": false,
    "error": "Error description",
    "error_type": "ErrorType",
    "timestamp": "2025-01-08 10:30:45"
}
```

Common HTTP status codes:
- `400`: Bad Request (invalid URL, validation errors)
- `500`: Internal Server Error (model errors, unexpected failures)
- `503`: Service Unavailable (model not loaded)

## 🧪 Testing

### Manual Testing
1. **Test the web interface** with various URLs
2. **Use the API documentation** at `/docs` for interactive testing
3. **Try edge cases** like invalid URLs, unreachable sites, etc.

### Example Test URLs
```python
# Legitimate URLs
test_urls = [
    "https://www.google.com",
    "https://github.com",
    "https://stackoverflow.com",
    "https://www.wikipedia.org"
]

# Test with curl
curl -X POST "http://localhost:8000/predict" \
     -H "Content-Type: application/json" \
     -d '{"url": "https://www.google.com"}'
```

## 📊 Feature Extraction

The system extracts exactly 30 features from each URL:

### URL Structure Features (1-11)
1. **LengthOfURL**: Total character count
2. **URLComplexity**: Complexity score based on structure
3. **TLD**: Top-level domain analysis
4. **LetterCntInURL**: Number of letters
5. **URLLetterRatio**: Ratio of letters to total characters
6. **DigitCntInURL**: Number of digits
7. **URLDigitRatio**: Ratio of digits to total characters
8. **OtherSpclCharCntInURL**: Special character count
9. **HavingPath**: Presence of path component
10. **PathLength**: Length of URL path
11. **HasSSL**: HTTPS protocol usage

### Content Features (12-30)
12. **LineOfCode**: Number of HTML lines
13. **LongestLineLength**: Maximum line length
14. **HasFavicon**: Favicon presence
15. **HasRobotsBlocked**: Robots.txt restrictions
16. **IsSelfRedirects**: Number of redirects
17. **HasDescription**: Meta description presence
18. **HasSubmitButton**: Form submission elements
19. **HasCopyrightInfoKey**: Copyright information
20. **CntImages**: Image count
21. **CntFilesCSS**: CSS file count
22. **CntFilesJS**: JavaScript file count
23. **CntSelfHRef**: Internal link count
24. **CntEmptyRef**: Empty reference count
25. **CntExternalRef**: External reference count
26. **CntIFrame**: Iframe element count
27. **UniqueFeatureCnt**: Unique HTML feature count
28. **ShannonEntropy**: URL entropy measure
29. **KolmogorovComplexity**: Compression-based complexity
30. **LikelinessIndex**: Overall suspicion score

## 🔧 Configuration

### Environment Variables
```bash
# Optional: Set log level
export LOG_LEVEL=INFO

# Optional: Set custom port
export PORT=8000
```

### Logging Configuration
Logs are stored in the `logs/` directory:
- `logs/app.log`: General application logs
- `logs/app_errors.log`: Error-only logs

## 🚨 Troubleshooting

### Common Issues

1. **Model file not found**
   - Ensure `stacking_ensemble.joblib` is in the project directory
   - Check file permissions

2. **Network timeouts**
   - Some websites may be slow or block requests
   - The system has built-in retry mechanisms

3. **Memory issues**
   - Ensure sufficient RAM (4GB minimum)
   - Monitor system resources

4. **Port already in use**
   - Change the port: `uvicorn main:app --port 8001`
   - Kill existing processes using the port

### Debug Mode
Run with debug logging:
```bash
python -c "
from logging_config import setup_logging
import logging
setup_logging(log_level=logging.DEBUG)
exec(open('main.py').read())
"
```

## 📈 Performance

### Expected Performance
- **Response Time**: 1-5 seconds per URL (depending on website speed)
- **Throughput**: 10-50 requests per minute
- **Memory Usage**: 200-500MB
- **Accuracy**: Based on training data performance

### Optimization Tips
1. **Batch Processing**: Use `/predict/batch` for multiple URLs
2. **Caching**: Consider implementing caching for repeated URLs
3. **Timeout Settings**: Adjust timeout values in `URLFeatureExtractor`

## 🔒 Security Considerations

1. **Input Validation**: All URLs are validated before processing
2. **Network Security**: Uses proper headers and timeouts
3. **Error Handling**: Sensitive information is not exposed in errors
4. **Resource Limits**: Batch size limits prevent abuse

## 📝 License

This project is for educational and research purposes. Please ensure compliance with applicable laws and website terms of service when analyzing URLs.

## 🤝 Support

For issues or questions:
1. Check the logs in `logs/` directory
2. Review the API documentation at `/docs`
3. Test with known working URLs first
4. Verify all dependencies are installed correctly
