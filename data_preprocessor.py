import pandas as pd
import numpy as np
import joblib
import logging
from sklearn.impute import SimpleImputer
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import Varian<PERSON><PERSON>hreshold, SelectKBest, mutual_info_classif
from category_encoders import TargetEncoder
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataPreprocessor:
    """
    Data preprocessing pipeline that exactly matches the training pipeline.
    
    The preprocessing steps follow this exact order:
    1. Missing Value Imputation (median for numeric, most frequent for categorical)
    2. Target Encoding for categorical features
    3. Variance Threshold feature selection
    4. StandardScaler for all numeric features
    5. SelectKBest feature selection
    """
    
    def __init__(self, model_path='stacking_ensemble.joblib'):
        """
        Initialize the preprocessor by loading the trained model and preprocessors.
        
        Args:
            model_path (str): Path to the saved model package
        """
        self.model_path = model_path
        self.model_package = None
        self.is_fitted = False
        
        # Load the model package
        self._load_model_package()
    
    def _load_model_package(self):
        """Load the complete model package with all preprocessors."""
        try:
            self.model_package = joblib.load(self.model_path)
            
            # Extract components
            self.stacking_classifier = self.model_package['stacking_classifier']
            self.target_encoder = self.model_package['target_encoder']
            self.variance_selector = self.model_package['variance_selector']
            self.scaler = self.model_package['scaler']
            self.feature_selector = self.model_package['feature_selector']
            self.selected_features = self.model_package['selected_features']
            self.feature_names_after_var = self.model_package['feature_names_after_var']
            self.categorical_cols = self.model_package['categorical_cols']
            self.numerical_cols = self.model_package['numerical_cols']
            self.num_imputer = self.model_package['num_imputer']
            self.cat_imputer = self.model_package['cat_imputer']
            
            self.is_fitted = True
            logger.info("Model package loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading model package: {str(e)}")
            raise
    
    def preprocess(self, features_df):
        """
        Preprocess features using the exact same pipeline as training.
        
        Args:
            features_df (pd.DataFrame): DataFrame with 30 features in correct order
            
        Returns:
            pd.DataFrame: Preprocessed features ready for model prediction
        """
        if not self.is_fitted:
            raise ValueError("Preprocessor not fitted. Model package not loaded.")
        
        try:
            # Make a copy to avoid modifying original data
            X = features_df.copy()
            
            logger.info(f"Starting preprocessing for {X.shape[0]} samples with {X.shape[1]} features")
            
            # Step 1: Handle Missing Values
            X = self._handle_missing_values(X)
            
            # Step 2: Target Encoding for Categorical Features
            X = self._apply_target_encoding(X)
            
            # Step 3: Variance Threshold Feature Selection
            X = self._apply_variance_threshold(X)
            
            # Step 4: Feature Scaling
            X = self._apply_scaling(X)
            
            # Step 5: SelectKBest Feature Selection
            X = self._apply_feature_selection(X)
            
            logger.info(f"Preprocessing completed. Final shape: {X.shape}")
            
            return X
            
        except Exception as e:
            logger.error(f"Error during preprocessing: {str(e)}")
            raise
    
    def _handle_missing_values(self, X):
        """Step 1: Handle missing values using the fitted imputers."""
        logger.info("Step 1: Handling missing values")
        
        # Identify current categorical and numerical columns
        current_categorical_cols = X.select_dtypes(include=['object', 'category']).columns.tolist()
        current_numerical_cols = X.select_dtypes(include=[np.number]).columns.tolist()
        
        # Impute numerical features with median (if imputer exists)
        if current_numerical_cols and self.num_imputer is not None:
            X[current_numerical_cols] = self.num_imputer.transform(X[current_numerical_cols])
            logger.info(f"Numerical features imputed: {len(current_numerical_cols)} columns")
        
        # Impute categorical features with most frequent (if imputer exists)
        if current_categorical_cols and self.cat_imputer is not None:
            X[current_categorical_cols] = self.cat_imputer.transform(X[current_categorical_cols])
            logger.info(f"Categorical features imputed: {len(current_categorical_cols)} columns")
        
        return X
    
    def _apply_target_encoding(self, X):
        """Step 2: Apply target encoding for categorical features."""
        logger.info("Step 2: Applying target encoding")
        
        if self.target_encoder is not None:
            # Apply the fitted target encoder
            X_encoded = self.target_encoder.transform(X)
            logger.info("Target encoding applied")
            return X_encoded
        else:
            logger.info("No target encoder found, skipping target encoding")
            return X
    
    def _apply_variance_threshold(self, X):
        """Step 3: Apply variance threshold feature selection."""
        logger.info("Step 3: Applying variance threshold")
        
        # Apply the fitted variance selector
        X_var = self.variance_selector.transform(X)
        
        # Convert back to DataFrame with correct feature names
        X_var_df = pd.DataFrame(X_var, columns=self.feature_names_after_var, index=X.index)
        
        logger.info(f"Variance threshold applied. Features: {X.shape[1]} -> {X_var_df.shape[1]}")
        
        return X_var_df
    
    def _apply_scaling(self, X):
        """Step 4: Apply StandardScaler to all features."""
        logger.info("Step 4: Applying feature scaling")
        
        # Apply the fitted scaler
        X_scaled = self.scaler.transform(X)
        
        logger.info("Feature scaling applied")
        
        return X_scaled
    
    def _apply_feature_selection(self, X_scaled):
        """Step 5: Apply SelectKBest feature selection."""
        logger.info("Step 5: Applying feature selection")
        
        # Apply the fitted feature selector
        X_final = self.feature_selector.transform(X_scaled)
        
        # Convert back to DataFrame with selected feature names
        X_final_df = pd.DataFrame(X_final, columns=self.selected_features)
        
        logger.info(f"Feature selection applied. Final features: {len(self.selected_features)}")
        
        return X_final_df
    
    def predict(self, features_df):
        """
        Complete pipeline: preprocess features and make prediction.
        
        Args:
            features_df (pd.DataFrame): DataFrame with 30 features in correct order
            
        Returns:
            dict: Prediction result with probability scores
        """
        try:
            # Preprocess the features
            X_processed = self.preprocess(features_df)
            
            # Make prediction
            prediction = self.stacking_classifier.predict(X_processed)[0]
            prediction_proba = self.stacking_classifier.predict_proba(X_processed)[0]
            
            # Prepare result
            result = {
                'prediction': int(prediction),
                'prediction_label': 'Phishing' if prediction == 1 else 'Legitimate',
                'confidence': float(max(prediction_proba)),
                'probabilities': {
                    'legitimate': float(prediction_proba[0]),
                    'phishing': float(prediction_proba[1])
                }
            }
            
            logger.info(f"Prediction completed: {result['prediction_label']} (confidence: {result['confidence']:.4f})")
            
            return result
            
        except Exception as e:
            logger.error(f"Error during prediction: {str(e)}")
            raise
    
    def get_feature_info(self):
        """Get information about the expected features and preprocessing steps."""
        if not self.is_fitted:
            return {"error": "Model not loaded"}
        
        return {
            "expected_features": self.selected_features,
            "num_features": len(self.selected_features),
            "categorical_columns": self.categorical_cols,
            "numerical_columns": self.numerical_cols,
            "preprocessing_steps": [
                "1. Missing value imputation",
                "2. Target encoding for categorical features", 
                "3. Variance threshold feature selection",
                "4. Standard scaling",
                "5. SelectKBest feature selection"
            ]
        }


# Example usage and testing
if __name__ == "__main__":
    # Test the preprocessor
    try:
        preprocessor = DataPreprocessor()
        
        # Get feature info
        info = preprocessor.get_feature_info()
        print("Feature Info:")
        print(f"Number of features: {info['num_features']}")
        print(f"Selected features: {info['expected_features'][:5]}...")
        
    except Exception as e:
        print(f"Error testing preprocessor: {e}")
