from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, validator
import logging
import time
import traceback
from typing import Optional
import uvicorn

from url_feature_extractor import URLFeatureExtractor
from data_preprocessor import DataPreprocessor
from logging_config import (
    setup_logging, log_function_call, log_api_request,
    ErrorHandler, performance_monitor, get_logger
)

# Setup comprehensive logging
setup_logging(log_level=logging.INFO)
logger = get_logger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Phishing URL Detection API",
    description="Production-ready API for detecting phishing URLs using machine learning",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables for model components
feature_extractor = None
preprocessor = None

# Pydantic models for request/response
class URLRequest(BaseModel):
    url: str
    
    @validator('url')
    def validate_url(cls, v):
        if not v or not v.strip():
            raise ValueError('URL cannot be empty')
        
        # Basic URL validation
        v = v.strip()
        if not any(v.startswith(prefix) for prefix in ['http://', 'https://', 'www.', 'ftp://']):
            # Try to add http:// if it looks like a domain
            if '.' in v and ' ' not in v:
                v = 'http://' + v
            else:
                raise ValueError('Invalid URL format')
        
        return v

class PredictionResponse(BaseModel):
    success: bool
    url: str
    prediction: int
    prediction_label: str
    confidence: float
    probabilities: dict
    processing_time: float
    timestamp: str

class ErrorResponse(BaseModel):
    success: bool
    error: str
    error_type: str
    timestamp: str

# Startup event
@app.on_event("startup")
@log_function_call(logger)
async def startup_event():
    """Initialize model components on startup."""
    global feature_extractor, preprocessor

    try:
        logger.info("Initializing application components...")

        # Initialize feature extractor
        feature_extractor = URLFeatureExtractor(timeout=15, max_retries=2)
        logger.info("Feature extractor initialized successfully")

        # Initialize preprocessor (loads the model)
        preprocessor = DataPreprocessor()
        logger.info("Data preprocessor and model loaded successfully")

        # Log model information
        model_info = preprocessor.get_feature_info()
        logger.info(f"Model loaded with {model_info['num_features']} features")

        logger.info("Application startup completed successfully")

    except Exception as e:
        error_info = ErrorHandler.handle_model_error(e)
        logger.error(f"Failed to initialize application: {error_info}")
        logger.error(traceback.format_exc())
        raise

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "components": {
            "feature_extractor": feature_extractor is not None,
            "preprocessor": preprocessor is not None
        }
    }

# Main prediction endpoint
@app.post("/predict", response_model=PredictionResponse)
@log_api_request(logger)
async def predict_url(request: Request, url_request: URLRequest):
    """
    Predict whether a URL is legitimate (0) or phishing (1).

    Args:
        request: FastAPI Request object
        url_request: URLRequest containing the URL to analyze

    Returns:
        PredictionResponse with prediction results
    """
    start_time = time.time()
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    success = False

    try:
        logger.info(f"Processing prediction request for URL: {url_request.url}")

        # Check if components are initialized
        if feature_extractor is None or preprocessor is None:
            error_info = ErrorHandler.handle_model_error(Exception("Service components not initialized"))
            raise HTTPException(
                status_code=503,
                detail=error_info["message"]
            )

        # Extract features from URL
        logger.info("Extracting features...")
        try:
            features_df = feature_extractor.extract_features(url_request.url)
        except Exception as e:
            error_info = ErrorHandler.handle_url_error(url_request.url, e)
            logger.warning(f"Feature extraction failed: {error_info}")
            raise HTTPException(
                status_code=400,
                detail=error_info["message"]
            )

        if features_df is None or features_df.empty:
            raise HTTPException(
                status_code=400,
                detail="Failed to extract features from URL. The website may be inaccessible."
            )

        # Make prediction
        logger.info("Making prediction...")
        try:
            result = preprocessor.predict(features_df)
        except Exception as e:
            error_info = ErrorHandler.handle_model_error(e)
            logger.error(f"Prediction failed: {error_info}")
            raise HTTPException(
                status_code=500,
                detail=error_info["message"]
            )

        # Calculate processing time
        processing_time = time.time() - start_time
        success = True

        # Prepare response
        response = PredictionResponse(
            success=True,
            url=url_request.url,
            prediction=result['prediction'],
            prediction_label=result['prediction_label'],
            confidence=result['confidence'],
            probabilities=result['probabilities'],
            processing_time=round(processing_time, 3),
            timestamp=timestamp
        )

        logger.info(f"Prediction completed: {result['prediction_label']} (confidence: {result['confidence']:.4f})")

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during prediction: {str(e)}")
        logger.error(traceback.format_exc())

        processing_time = time.time() - start_time

        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "An unexpected error occurred during analysis",
                "error_type": type(e).__name__,
                "processing_time": round(processing_time, 3),
                "timestamp": timestamp
            }
        )
    finally:
        # Record performance metrics
        processing_time = time.time() - start_time
        performance_monitor.record_request(success=success, response_time=processing_time)

# Batch prediction endpoint
@app.post("/predict/batch")
@log_api_request(logger)
async def predict_batch(request: Request, urls: list[str]):
    """
    Predict multiple URLs in batch.

    Args:
        request: FastAPI Request object
        urls: List of URLs to analyze

    Returns:
        List of prediction results
    """
    if len(urls) > 10:  # Limit batch size
        logger.warning(f"Batch size limit exceeded: {len(urls)} URLs requested")
        raise HTTPException(
            status_code=400,
            detail="Batch size limited to 10 URLs"
        )

    logger.info(f"Processing batch prediction for {len(urls)} URLs")
    results = []
    successful_predictions = 0

    for i, url in enumerate(urls):
        try:
            url_request = URLRequest(url=url)
            # Create a mock request object for the individual prediction
            result = await predict_url(request, url_request)
            results.append(result.dict())
            successful_predictions += 1
            logger.debug(f"Batch item {i+1}/{len(urls)} completed successfully")
        except Exception as e:
            error_info = ErrorHandler.handle_url_error(url, e)
            results.append({
                "success": False,
                "url": url,
                "error": error_info["message"],
                "error_type": error_info["error_type"],
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            })
            logger.warning(f"Batch item {i+1}/{len(urls)} failed: {error_info['message']}")

    logger.info(f"Batch prediction completed: {successful_predictions}/{len(urls)} successful")
    return {
        "results": results,
        "summary": {
            "total": len(urls),
            "successful": successful_predictions,
            "failed": len(urls) - successful_predictions
        }
    }

# Performance metrics endpoint
@app.get("/metrics")
async def get_performance_metrics():
    """Get current performance metrics."""
    try:
        metrics = performance_monitor.get_metrics()
        return {
            "performance_metrics": metrics,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
    except Exception as e:
        logger.error(f"Error retrieving metrics: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve metrics")

# Model info endpoint
@app.get("/model/info")
async def get_model_info():
    """Get information about the loaded model."""
    try:
        if preprocessor is None:
            raise HTTPException(status_code=503, detail="Model not loaded")
        
        info = preprocessor.get_feature_info()
        return {
            "model_info": info,
            "feature_extractor_config": {
                "timeout": feature_extractor.timeout if feature_extractor else None,
                "max_retries": feature_extractor.max_retries if feature_extractor else None
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Serve static files (for web interface)
app.mount("/static", StaticFiles(directory="static"), name="static")

# Root endpoint - serve web interface
@app.get("/", response_class=HTMLResponse)
async def read_root():
    """Serve the main web interface."""
    try:
        with open("static/index.html", "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        return HTMLResponse(
            content="""
            <html>
                <head><title>Phishing URL Detection</title></head>
                <body>
                    <h1>Phishing URL Detection API</h1>
                    <p>Web interface not found. Please check the static files.</p>
                    <p><a href="/docs">API Documentation</a></p>
                </body>
            </html>
            """,
            status_code=200
        )

# Custom exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {str(exc)}")
    logger.error(traceback.format_exc())
    
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "Internal server error",
            "error_type": type(exc).__name__,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
    )

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
