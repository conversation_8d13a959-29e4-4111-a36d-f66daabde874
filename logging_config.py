import logging
import logging.handlers
import os
import sys
from datetime import datetime
import traceback
from functools import wraps
import time

def setup_logging(log_level=logging.INFO, log_file=None, max_bytes=10*1024*1024, backup_count=5):
    """
    Set up comprehensive logging configuration.
    
    Args:
        log_level: Logging level (default: INFO)
        log_file: Log file path (default: logs/app.log)
        max_bytes: Maximum log file size before rotation
        backup_count: Number of backup files to keep
    """
    
    # Create logs directory if it doesn't exist
    if log_file is None:
        os.makedirs('logs', exist_ok=True)
        log_file = 'logs/app.log'
    else:
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Create root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        log_file, maxBytes=max_bytes, backupCount=backup_count
    )
    file_handler.setLevel(log_level)
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)
    
    # Error file handler (only errors and critical)
    error_log_file = log_file.replace('.log', '_errors.log')
    error_handler = logging.handlers.RotatingFileHandler(
        error_log_file, maxBytes=max_bytes, backupCount=backup_count
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(formatter)
    root_logger.addHandler(error_handler)
    
    logging.info(f"Logging configured - Level: {logging.getLevelName(log_level)}, File: {log_file}")

def log_function_call(logger=None):
    """
    Decorator to log function calls with execution time and error handling.
    
    Args:
        logger: Logger instance to use (default: creates one based on module name)
    """
    def decorator(func):
        nonlocal logger
        if logger is None:
            logger = logging.getLogger(func.__module__)
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            func_name = func.__name__
            
            # Log function entry
            logger.debug(f"Entering {func_name} with args={args}, kwargs={kwargs}")
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger.debug(f"Exiting {func_name} successfully in {execution_time:.3f}s")
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"Error in {func_name} after {execution_time:.3f}s: {str(e)}")
                logger.error(f"Traceback: {traceback.format_exc()}")
                raise
        
        return wrapper
    return decorator

def log_api_request(logger=None):
    """
    Decorator specifically for API request logging.
    """
    def decorator(func):
        nonlocal logger
        if logger is None:
            logger = logging.getLogger(func.__module__)
        
        @wraps(func)
        async def wrapper(request, *args, **kwargs):
            start_time = time.time()
            client_ip = getattr(request, 'client', {}).get('host', 'unknown')
            user_agent = request.headers.get('user-agent', 'unknown')
            
            # Log request
            logger.info(f"API Request: {func.__name__} from {client_ip} - User-Agent: {user_agent}")
            
            try:
                result = await func(request, *args, **kwargs)
                execution_time = time.time() - start_time
                logger.info(f"API Response: {func.__name__} completed in {execution_time:.3f}s")
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"API Error: {func.__name__} failed after {execution_time:.3f}s - {str(e)}")
                logger.error(f"Traceback: {traceback.format_exc()}")
                raise
        
        return wrapper
    return decorator

class ErrorHandler:
    """
    Centralized error handling utilities.
    """
    
    @staticmethod
    def handle_url_error(url, error):
        """Handle URL-related errors."""
        logger = logging.getLogger(__name__)
        
        error_type = type(error).__name__
        error_msg = str(error)
        
        if "timeout" in error_msg.lower():
            logger.warning(f"Timeout error for URL {url}: {error_msg}")
            return {
                "error_type": "timeout",
                "message": "Request timed out. The website may be slow or unreachable.",
                "recoverable": True
            }
        elif "connection" in error_msg.lower():
            logger.warning(f"Connection error for URL {url}: {error_msg}")
            return {
                "error_type": "connection",
                "message": "Could not connect to the website. Please check the URL.",
                "recoverable": True
            }
        elif "ssl" in error_msg.lower() or "certificate" in error_msg.lower():
            logger.warning(f"SSL error for URL {url}: {error_msg}")
            return {
                "error_type": "ssl",
                "message": "SSL certificate error. The website may have security issues.",
                "recoverable": False
            }
        elif "404" in error_msg or "not found" in error_msg.lower():
            logger.warning(f"Not found error for URL {url}: {error_msg}")
            return {
                "error_type": "not_found",
                "message": "The requested page was not found (404 error).",
                "recoverable": False
            }
        else:
            logger.error(f"Unexpected error for URL {url}: {error_msg}")
            return {
                "error_type": "unknown",
                "message": f"An unexpected error occurred: {error_msg}",
                "recoverable": False
            }
    
    @staticmethod
    def handle_model_error(error):
        """Handle model-related errors."""
        logger = logging.getLogger(__name__)
        
        error_type = type(error).__name__
        error_msg = str(error)
        
        if "shape" in error_msg.lower() or "dimension" in error_msg.lower():
            logger.error(f"Model input shape error: {error_msg}")
            return {
                "error_type": "input_shape",
                "message": "Feature extraction failed. Invalid input format.",
                "recoverable": False
            }
        elif "memory" in error_msg.lower():
            logger.error(f"Model memory error: {error_msg}")
            return {
                "error_type": "memory",
                "message": "Insufficient memory to process the request.",
                "recoverable": True
            }
        elif "model" in error_msg.lower() or "joblib" in error_msg.lower():
            logger.error(f"Model loading error: {error_msg}")
            return {
                "error_type": "model_loading",
                "message": "Model loading failed. Please contact support.",
                "recoverable": False
            }
        else:
            logger.error(f"Unexpected model error: {error_msg}")
            return {
                "error_type": "model_unknown",
                "message": f"Model prediction failed: {error_msg}",
                "recoverable": False
            }
    
    @staticmethod
    def handle_validation_error(error):
        """Handle input validation errors."""
        logger = logging.getLogger(__name__)
        
        error_msg = str(error)
        logger.warning(f"Validation error: {error_msg}")
        
        return {
            "error_type": "validation",
            "message": f"Invalid input: {error_msg}",
            "recoverable": True
        }

class PerformanceMonitor:
    """
    Monitor application performance and log metrics.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.metrics = {
            'requests_total': 0,
            'requests_successful': 0,
            'requests_failed': 0,
            'avg_response_time': 0,
            'total_response_time': 0
        }
    
    def record_request(self, success=True, response_time=0):
        """Record request metrics."""
        self.metrics['requests_total'] += 1
        self.metrics['total_response_time'] += response_time
        
        if success:
            self.metrics['requests_successful'] += 1
        else:
            self.metrics['requests_failed'] += 1
        
        # Update average response time
        if self.metrics['requests_total'] > 0:
            self.metrics['avg_response_time'] = (
                self.metrics['total_response_time'] / self.metrics['requests_total']
            )
        
        # Log metrics every 10 requests
        if self.metrics['requests_total'] % 10 == 0:
            self.log_metrics()
    
    def log_metrics(self):
        """Log current performance metrics."""
        success_rate = (
            self.metrics['requests_successful'] / self.metrics['requests_total'] * 100
            if self.metrics['requests_total'] > 0 else 0
        )
        
        self.logger.info(
            f"Performance Metrics - "
            f"Total: {self.metrics['requests_total']}, "
            f"Success Rate: {success_rate:.1f}%, "
            f"Avg Response Time: {self.metrics['avg_response_time']:.3f}s"
        )
    
    def get_metrics(self):
        """Get current metrics."""
        return self.metrics.copy()

# Global performance monitor instance
performance_monitor = PerformanceMonitor()

# Utility function to create logger for specific modules
def get_logger(name):
    """Get a logger for a specific module."""
    return logging.getLogger(name)

# Example usage
if __name__ == "__main__":
    # Setup logging
    setup_logging(log_level=logging.DEBUG)
    
    # Test logging
    logger = get_logger(__name__)
    logger.info("Logging configuration test")
    logger.warning("This is a warning")
    logger.error("This is an error")
    
    # Test error handler
    try:
        raise ConnectionError("Test connection error")
    except Exception as e:
        error_info = ErrorHandler.handle_url_error("https://test.com", e)
        logger.info(f"Error handled: {error_info}")
    
    # Test performance monitor
    performance_monitor.record_request(success=True, response_time=1.5)
    performance_monitor.record_request(success=False, response_time=0.8)
    metrics = performance_monitor.get_metrics()
    logger.info(f"Current metrics: {metrics}")
