# FastAPI and web server
fastapi>=0.68.0
uvicorn[standard]>=0.15.0

# Data processing and machine learning
pandas>=1.3.0
numpy>=1.21.0
scikit-learn>=1.0.0
joblib>=1.0.0

# Feature extraction dependencies
requests>=2.25.0
beautifulsoup4>=4.9.0
tldextract>=3.1.0

# Machine learning models
lightgbm>=3.2.0
xgboost>=1.4.0
category-encoders>=2.3.0

# API and validation
pydantic>=1.8.0

# Optional: For enhanced performance
# psutil>=5.8.0  # For system monitoring
# aiofiles>=0.7.0  # For async file operations
