import pandas as pd
import numpy as np
import requests
import re
import math
import time
import logging
from urllib.parse import urlparse, urljoin
from bs4 import BeautifulSoup
from collections import Counter
import tldextract
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class URLFeatureExtractor:
    """
    Comprehensive URL feature extractor that extracts exactly 30 features
    from URLs by fetching actual web content via HTTP requests.
    """
    
    def __init__(self, timeout=10, max_retries=2):
        """
        Initialize the feature extractor.
        
        Args:
            timeout (int): Request timeout in seconds
            max_retries (int): Maximum number of retries for failed requests
        """
        self.timeout = timeout
        self.max_retries = max_retries
        self.session = requests.Session()
        
        # Set user agent to avoid blocking
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def extract_features(self, url):
        """
        Extract all 30 features from a given URL.
        
        Args:
            url (str): The URL to analyze
            
        Returns:
            pd.DataFrame: DataFrame with exactly 30 features in the correct order
        """
        try:
            # Normalize URL
            if not url.startswith(('http://', 'https://')):
                url = 'http://' + url
            
            # Parse URL
            parsed_url = urlparse(url)
            
            # Fetch web content
            html_content, response_info = self._fetch_content(url)
            
            # Extract all features
            features = {}
            
            # 1. LengthOfURL
            features['LengthOfURL'] = len(url)
            
            # 2. URLComplexity
            features['URLComplexity'] = self._calculate_url_complexity(url)
            
            # 3. TLD
            features['TLD'] = self._extract_tld_feature(url)
            
            # 4. LetterCntInURL
            features['LetterCntInURL'] = sum(c.isalpha() for c in url)
            
            # 5. URLLetterRatio
            features['URLLetterRatio'] = features['LetterCntInURL'] / len(url) if len(url) > 0 else 0
            
            # 6. DigitCntInURL
            features['DigitCntInURL'] = sum(c.isdigit() for c in url)
            
            # 7. URLDigitRatio
            features['URLDigitRatio'] = features['DigitCntInURL'] / len(url) if len(url) > 0 else 0
            
            # 8. OtherSpclCharCntInURL
            features['OtherSpclCharCntInURL'] = self._count_special_chars(url)
            
            # 9. HavingPath
            features['HavingPath'] = 1 if parsed_url.path and parsed_url.path != '/' else 0
            
            # 10. PathLength
            features['PathLength'] = len(parsed_url.path) if parsed_url.path else 0
            
            # 11. HasSSL
            features['HasSSL'] = 1 if parsed_url.scheme == 'https' else 0
            
            # Features requiring HTML content
            if html_content:
                soup = BeautifulSoup(html_content, 'html.parser')
                
                # 12. LineOfCode
                features['LineOfCode'] = len(html_content.split('\n'))
                
                # 13. LongestLineLength
                lines = html_content.split('\n')
                features['LongestLineLength'] = max(len(line) for line in lines) if lines else 0
                
                # 14. HasFavicon
                features['HasFavicon'] = self._has_favicon(soup, url)
                
                # 15. HasRobotsBlocked
                features['HasRobotsBlocked'] = self._check_robots_blocked(url)
                
                # 16. IsSelfRedirects
                features['IsSelfRedirects'] = response_info.get('redirects', 0)
                
                # 17. HasDescription
                features['HasDescription'] = self._has_description(soup)
                
                # 18. HasSubmitButton
                features['HasSubmitButton'] = self._has_submit_button(soup)
                
                # 19. HasCopyrightInfoKey (note: trailing space in name)
                features['HasCopyrightInfoKey '] = self._has_copyright_info(soup)
                
                # 20. CntImages
                features['CntImages'] = len(soup.find_all('img'))
                
                # 21. CntFilesCSS
                features['CntFilesCSS'] = self._count_css_files(soup)
                
                # 22. CntFilesJS
                features['CntFilesJS'] = self._count_js_files(soup)
                
                # 23. CntSelfHRef
                features['CntSelfHRef'] = self._count_self_href(soup, url)
                
                # 24. CntEmptyRef
                features['CntEmptyRef'] = self._count_empty_ref(soup)
                
                # 25. CntExternalRef
                features['CntExternalRef'] = self._count_external_ref(soup, url)
                
                # 26. CntIFrame
                features['CntIFrame'] = len(soup.find_all('iframe'))
                
                # 27. UniqueFeatureCnt
                features['UniqueFeatureCnt'] = self._calculate_unique_features(soup)
                
                # 28. ShannonEntropy
                features['ShannonEntropy'] = self._calculate_shannon_entropy(url)
                
                # 29. KolmogorovComplexity
                features['KolmogorovComplexity'] = self._calculate_kolmogorov_complexity(url)
                
                # 30. LikelinessIndex
                features['LikelinessIndex'] = self._calculate_likeliness_index(url, soup)
                
            else:
                # Default values when HTML content is not available
                default_features = {
                    'LineOfCode': 0, 'LongestLineLength': 0, 'HasFavicon': 0,
                    'HasRobotsBlocked': 0, 'IsSelfRedirects': 0, 'HasDescription': 0,
                    'HasSubmitButton': 0, 'HasCopyrightInfoKey ': 0, 'CntImages': 0,
                    'CntFilesCSS': 0, 'CntFilesJS': 0, 'CntSelfHRef': 0,
                    'CntEmptyRef': 0, 'CntExternalRef': 0, 'CntIFrame': 0,
                    'UniqueFeatureCnt': 0, 'ShannonEntropy': self._calculate_shannon_entropy(url),
                    'KolmogorovComplexity': self._calculate_kolmogorov_complexity(url),
                    'LikelinessIndex': 0
                }
                features.update(default_features)
            
            # Create DataFrame with exact feature order
            feature_order = [
                'LengthOfURL', 'URLComplexity', 'TLD', 'LetterCntInURL', 'URLLetterRatio',
                'DigitCntInURL', 'URLDigitRatio', 'OtherSpclCharCntInURL', 'HavingPath',
                'PathLength', 'HasSSL', 'LineOfCode', 'LongestLineLength', 'HasFavicon',
                'HasRobotsBlocked', 'IsSelfRedirects', 'HasDescription', 'HasSubmitButton',
                'HasCopyrightInfoKey ', 'CntImages', 'CntFilesCSS', 'CntFilesJS',
                'CntSelfHRef', 'CntEmptyRef', 'CntExternalRef', 'CntIFrame',
                'UniqueFeatureCnt', 'ShannonEntropy', 'KolmogorovComplexity', 'LikelinessIndex'
            ]
            
            # Ensure all features are present and in correct order
            ordered_features = {feature: features.get(feature, 0) for feature in feature_order}
            
            return pd.DataFrame([ordered_features])
            
        except Exception as e:
            logger.error(f"Error extracting features from {url}: {str(e)}")
            # Return DataFrame with default values
            return self._get_default_features()

    def _fetch_content(self, url):
        """
        Fetch HTML content from URL with retries and error handling.

        Args:
            url (str): URL to fetch

        Returns:
            tuple: (html_content, response_info)
        """
        for attempt in range(self.max_retries + 1):
            try:
                response = self.session.get(url, timeout=self.timeout, allow_redirects=True)
                response.raise_for_status()

                response_info = {
                    'status_code': response.status_code,
                    'redirects': len(response.history),
                    'final_url': response.url
                }

                return response.text, response_info

            except requests.exceptions.RequestException as e:
                logger.warning(f"Attempt {attempt + 1} failed for {url}: {str(e)}")
                if attempt == self.max_retries:
                    logger.error(f"Failed to fetch {url} after {self.max_retries + 1} attempts")
                    return None, {'status_code': 0, 'redirects': 0, 'final_url': url}
                time.sleep(1)  # Wait before retry

        return None, {'status_code': 0, 'redirects': 0, 'final_url': url}

    def _calculate_url_complexity(self, url):
        """Calculate URL complexity based on various factors."""
        complexity = 0

        # Length factor
        complexity += len(url) / 100

        # Special characters
        special_chars = re.findall(r'[^a-zA-Z0-9]', url)
        complexity += len(special_chars) / 10

        # Subdomain count
        parsed = urlparse(url)
        if parsed.netloc:
            subdomains = parsed.netloc.split('.')
            complexity += len(subdomains) / 5

        # Path depth
        if parsed.path:
            path_depth = len([p for p in parsed.path.split('/') if p])
            complexity += path_depth / 3

        return round(complexity, 4)

    def _extract_tld_feature(self, url):
        """Extract TLD feature using tldextract."""
        try:
            extracted = tldextract.extract(url)
            tld = extracted.suffix.lower()

            # Common legitimate TLDs get lower scores
            legitimate_tlds = ['com', 'org', 'net', 'edu', 'gov', 'mil']
            suspicious_tlds = ['tk', 'ml', 'ga', 'cf', 'bit', 'onion']

            if tld in legitimate_tlds:
                return 0
            elif tld in suspicious_tlds:
                return 2
            else:
                return 1

        except Exception:
            return 1

    def _count_special_chars(self, url):
        """Count special characters in URL excluding common ones."""
        # Exclude common URL characters
        common_chars = set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789:/.?=&-_')
        special_count = sum(1 for c in url if c not in common_chars)
        return special_count

    def _has_favicon(self, soup, url):
        """Check if the page has a favicon."""
        try:
            # Check for favicon in HTML
            favicon_tags = soup.find_all('link', rel=lambda x: x and 'icon' in x.lower())
            if favicon_tags:
                return 1

            # Check for default favicon.ico
            parsed_url = urlparse(url)
            favicon_url = f"{parsed_url.scheme}://{parsed_url.netloc}/favicon.ico"

            try:
                response = self.session.head(favicon_url, timeout=5)
                return 1 if response.status_code == 200 else 0
            except:
                return 0

        except Exception:
            return 0

    def _check_robots_blocked(self, url):
        """Check if robots.txt blocks crawling."""
        try:
            parsed_url = urlparse(url)
            robots_url = f"{parsed_url.scheme}://{parsed_url.netloc}/robots.txt"

            response = self.session.get(robots_url, timeout=5)
            if response.status_code == 200:
                robots_content = response.text.lower()
                if 'disallow: /' in robots_content:
                    return 1
            return 0

        except Exception:
            return 0

    def _has_description(self, soup):
        """Check if page has meta description."""
        try:
            description = soup.find('meta', attrs={'name': 'description'})
            return 1 if description and description.get('content') else 0
        except Exception:
            return 0

    def _has_submit_button(self, soup):
        """Check if page has submit buttons."""
        try:
            submit_buttons = soup.find_all(['input', 'button'], type='submit')
            submit_buttons += soup.find_all('button', string=re.compile(r'submit', re.I))
            return 1 if submit_buttons else 0
        except Exception:
            return 0

    def _has_copyright_info(self, soup):
        """Check if page has copyright information."""
        try:
            text_content = soup.get_text().lower()
            copyright_patterns = [r'copyright', r'©', r'\(c\)', r'all rights reserved']

            for pattern in copyright_patterns:
                if re.search(pattern, text_content):
                    return 1
            return 0
        except Exception:
            return 0

    def _count_css_files(self, soup):
        """Count CSS files referenced in the page."""
        try:
            css_links = soup.find_all('link', rel='stylesheet')
            css_styles = soup.find_all('style')
            return len(css_links) + len(css_styles)
        except Exception:
            return 0

    def _count_js_files(self, soup):
        """Count JavaScript files referenced in the page."""
        try:
            js_scripts = soup.find_all('script', src=True)
            inline_scripts = soup.find_all('script', src=False)
            return len(js_scripts) + len(inline_scripts)
        except Exception:
            return 0

    def _count_self_href(self, soup, url):
        """Count links that reference the same domain."""
        try:
            parsed_url = urlparse(url)
            domain = parsed_url.netloc.lower()

            links = soup.find_all('a', href=True)
            self_links = 0

            for link in links:
                href = link['href']
                if href.startswith('/') or domain in href.lower():
                    self_links += 1

            return self_links
        except Exception:
            return 0

    def _count_empty_ref(self, soup):
        """Count empty or invalid references."""
        try:
            empty_count = 0

            # Empty href attributes
            links = soup.find_all('a', href=True)
            empty_count += sum(1 for link in links if not link['href'].strip() or link['href'] in ['#', 'javascript:void(0)'])

            # Empty src attributes
            images = soup.find_all('img', src=True)
            empty_count += sum(1 for img in images if not img['src'].strip())

            return empty_count
        except Exception:
            return 0

    def _count_external_ref(self, soup, url):
        """Count external references (different domains)."""
        try:
            parsed_url = urlparse(url)
            domain = parsed_url.netloc.lower()

            external_count = 0

            # External links
            links = soup.find_all('a', href=True)
            for link in links:
                href = link['href']
                if href.startswith('http') and domain not in href.lower():
                    external_count += 1

            # External images
            images = soup.find_all('img', src=True)
            for img in images:
                src = img['src']
                if src.startswith('http') and domain not in src.lower():
                    external_count += 1

            return external_count
        except Exception:
            return 0

    def _calculate_unique_features(self, soup):
        """Calculate number of unique HTML features."""
        try:
            unique_features = set()

            # Unique tags
            for tag in soup.find_all():
                unique_features.add(tag.name)

            # Unique classes
            for tag in soup.find_all(class_=True):
                if isinstance(tag['class'], list):
                    unique_features.update(tag['class'])
                else:
                    unique_features.add(tag['class'])

            # Unique IDs
            for tag in soup.find_all(id=True):
                unique_features.add(tag['id'])

            return len(unique_features)
        except Exception:
            return 0

    def _calculate_shannon_entropy(self, url):
        """Calculate Shannon entropy of the URL."""
        try:
            # Count character frequencies
            char_counts = Counter(url)
            url_length = len(url)

            # Calculate entropy
            entropy = 0
            for count in char_counts.values():
                probability = count / url_length
                if probability > 0:
                    entropy -= probability * math.log2(probability)

            return round(entropy, 4)
        except Exception:
            return 0

    def _calculate_kolmogorov_complexity(self, url):
        """Estimate Kolmogorov complexity using compression ratio."""
        try:
            import zlib

            # Compress the URL
            compressed = zlib.compress(url.encode('utf-8'))

            # Calculate compression ratio as complexity measure
            complexity = len(compressed) / len(url) if len(url) > 0 else 0

            return round(complexity, 4)
        except Exception:
            return 0

    def _calculate_likeliness_index(self, url, soup):
        """Calculate overall likeliness index based on multiple factors."""
        try:
            score = 0

            # URL-based factors
            parsed_url = urlparse(url)

            # Domain factors
            if parsed_url.netloc:
                domain_parts = parsed_url.netloc.split('.')
                if len(domain_parts) > 3:  # Many subdomains
                    score += 0.2

                # IP address instead of domain
                if re.match(r'\d+\.\d+\.\d+\.\d+', parsed_url.netloc):
                    score += 0.5

            # URL length factor
            if len(url) > 100:
                score += 0.1

            # Special characters factor
            special_ratio = self._count_special_chars(url) / len(url) if len(url) > 0 else 0
            score += special_ratio * 0.3

            # Content-based factors if available
            if soup:
                # Missing title
                if not soup.find('title'):
                    score += 0.1

                # Few images
                if len(soup.find_all('img')) < 2:
                    score += 0.1

            return round(min(score, 1.0), 4)  # Cap at 1.0
        except Exception:
            return 0

    def _get_default_features(self):
        """Return DataFrame with default feature values."""
        feature_order = [
            'LengthOfURL', 'URLComplexity', 'TLD', 'LetterCntInURL', 'URLLetterRatio',
            'DigitCntInURL', 'URLDigitRatio', 'OtherSpclCharCntInURL', 'HavingPath',
            'PathLength', 'HasSSL', 'LineOfCode', 'LongestLineLength', 'HasFavicon',
            'HasRobotsBlocked', 'IsSelfRedirects', 'HasDescription', 'HasSubmitButton',
            'HasCopyrightInfoKey ', 'CntImages', 'CntFilesCSS', 'CntFilesJS',
            'CntSelfHRef', 'CntEmptyRef', 'CntExternalRef', 'CntIFrame',
            'UniqueFeatureCnt', 'ShannonEntropy', 'KolmogorovComplexity', 'LikelinessIndex'
        ]

        default_values = {feature: 0 for feature in feature_order}
        return pd.DataFrame([default_values])


# Example usage and testing
if __name__ == "__main__":
    # Initialize the extractor
    extractor = URLFeatureExtractor()

    # Test URLs
    test_urls = [
        "https://www.google.com",
        "http://example.com",
        "https://github.com"
    ]

    for url in test_urls:
        print(f"\nExtracting features for: {url}")
        try:
            features_df = extractor.extract_features(url)
            print(f"Features extracted successfully. Shape: {features_df.shape}")
            print("First 5 features:")
            print(features_df.iloc[:, :5].to_string())
        except Exception as e:
            print(f"Error: {e}")
