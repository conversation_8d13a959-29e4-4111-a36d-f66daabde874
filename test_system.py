#!/usr/bin/env python3
"""
Test script for the Phishing URL Detection System.
This script validates the complete pipeline functionality.
"""

import requests
import json
import time
import sys
from urllib.parse import urlparse

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_TIMEOUT = 30  # seconds

# Test URLs - mix of legitimate and potentially suspicious
TEST_URLS = [
    "https://www.google.com",
    "https://github.com",
    "https://stackoverflow.com",
    "https://www.wikipedia.org",
    "http://example.com",
    "https://www.microsoft.com"
]

class SystemTester:
    """Test suite for the phishing detection system."""
    
    def __init__(self, base_url=BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.timeout = TEST_TIMEOUT
        self.results = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'errors': []
        }
    
    def log(self, message, level="INFO"):
        """Log test messages."""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def test_health_check(self):
        """Test the health check endpoint."""
        self.log("Testing health check endpoint...")
        self.results['total_tests'] += 1
        
        try:
            response = self.session.get(f"{self.base_url}/health")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'healthy':
                    self.log("✅ Health check passed")
                    self.results['passed_tests'] += 1
                    return True
                else:
                    self.log(f"❌ Health check failed: {data}", "ERROR")
            else:
                self.log(f"❌ Health check failed with status {response.status_code}", "ERROR")
        
        except Exception as e:
            self.log(f"❌ Health check error: {str(e)}", "ERROR")
            self.results['errors'].append(f"Health check: {str(e)}")
        
        self.results['failed_tests'] += 1
        return False
    
    def test_model_info(self):
        """Test the model info endpoint."""
        self.log("Testing model info endpoint...")
        self.results['total_tests'] += 1
        
        try:
            response = self.session.get(f"{self.base_url}/model/info")
            
            if response.status_code == 200:
                data = response.json()
                model_info = data.get('model_info', {})
                
                if model_info.get('num_features') == 30:
                    self.log("✅ Model info test passed")
                    self.log(f"   Features: {model_info.get('num_features')}")
                    self.results['passed_tests'] += 1
                    return True
                else:
                    self.log(f"❌ Model info test failed: unexpected feature count", "ERROR")
            else:
                self.log(f"❌ Model info test failed with status {response.status_code}", "ERROR")
        
        except Exception as e:
            self.log(f"❌ Model info error: {str(e)}", "ERROR")
            self.results['errors'].append(f"Model info: {str(e)}")
        
        self.results['failed_tests'] += 1
        return False
    
    def test_single_prediction(self, url):
        """Test single URL prediction."""
        self.log(f"Testing prediction for: {url}")
        self.results['total_tests'] += 1
        
        try:
            payload = {"url": url}
            response = self.session.post(
                f"{self.base_url}/predict",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Validate response structure
                required_fields = ['success', 'url', 'prediction', 'prediction_label', 
                                 'confidence', 'probabilities', 'processing_time']
                
                if all(field in data for field in required_fields):
                    if data['prediction'] in [0, 1] and data['success']:
                        self.log(f"✅ Prediction successful: {data['prediction_label']} "
                               f"(confidence: {data['confidence']:.3f})")
                        self.results['passed_tests'] += 1
                        return True
                    else:
                        self.log(f"❌ Invalid prediction data: {data}", "ERROR")
                else:
                    missing = [f for f in required_fields if f not in data]
                    self.log(f"❌ Missing fields in response: {missing}", "ERROR")
            else:
                self.log(f"❌ Prediction failed with status {response.status_code}: {response.text}", "ERROR")
        
        except Exception as e:
            self.log(f"❌ Prediction error: {str(e)}", "ERROR")
            self.results['errors'].append(f"Prediction for {url}: {str(e)}")
        
        self.results['failed_tests'] += 1
        return False
    
    def test_batch_prediction(self, urls):
        """Test batch URL prediction."""
        self.log(f"Testing batch prediction for {len(urls)} URLs...")
        self.results['total_tests'] += 1
        
        try:
            response = self.session.post(
                f"{self.base_url}/predict/batch",
                json=urls,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if 'results' in data and 'summary' in data:
                    results = data['results']
                    summary = data['summary']
                    
                    if len(results) == len(urls) and summary['total'] == len(urls):
                        successful = sum(1 for r in results if r.get('success', False))
                        self.log(f"✅ Batch prediction successful: {successful}/{len(urls)} URLs processed")
                        self.results['passed_tests'] += 1
                        return True
                    else:
                        self.log(f"❌ Batch prediction: result count mismatch", "ERROR")
                else:
                    self.log(f"❌ Batch prediction: invalid response structure", "ERROR")
            else:
                self.log(f"❌ Batch prediction failed with status {response.status_code}", "ERROR")
        
        except Exception as e:
            self.log(f"❌ Batch prediction error: {str(e)}", "ERROR")
            self.results['errors'].append(f"Batch prediction: {str(e)}")
        
        self.results['failed_tests'] += 1
        return False
    
    def test_invalid_url(self):
        """Test handling of invalid URLs."""
        self.log("Testing invalid URL handling...")
        self.results['total_tests'] += 1
        
        invalid_urls = ["not-a-url", "http://", "invalid.url.format"]
        
        for invalid_url in invalid_urls:
            try:
                payload = {"url": invalid_url}
                response = self.session.post(
                    f"{self.base_url}/predict",
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )
                
                # Should return 400 or handle gracefully
                if response.status_code in [400, 422]:
                    self.log(f"✅ Invalid URL properly rejected: {invalid_url}")
                    self.results['passed_tests'] += 1
                    return True
                elif response.status_code == 200:
                    # If it processes, check if it returns an error in the response
                    data = response.json()
                    if not data.get('success', True):
                        self.log(f"✅ Invalid URL handled gracefully: {invalid_url}")
                        self.results['passed_tests'] += 1
                        return True
            
            except Exception as e:
                self.log(f"❌ Error testing invalid URL {invalid_url}: {str(e)}", "ERROR")
        
        self.log("❌ Invalid URL test failed", "ERROR")
        self.results['failed_tests'] += 1
        return False
    
    def test_metrics_endpoint(self):
        """Test the metrics endpoint."""
        self.log("Testing metrics endpoint...")
        self.results['total_tests'] += 1
        
        try:
            response = self.session.get(f"{self.base_url}/metrics")
            
            if response.status_code == 200:
                data = response.json()
                
                if 'performance_metrics' in data:
                    metrics = data['performance_metrics']
                    required_metrics = ['requests_total', 'requests_successful', 'requests_failed']
                    
                    if all(metric in metrics for metric in required_metrics):
                        self.log("✅ Metrics endpoint test passed")
                        self.log(f"   Total requests: {metrics.get('requests_total', 0)}")
                        self.results['passed_tests'] += 1
                        return True
                    else:
                        self.log(f"❌ Metrics missing required fields", "ERROR")
                else:
                    self.log(f"❌ Metrics response invalid structure", "ERROR")
            else:
                self.log(f"❌ Metrics endpoint failed with status {response.status_code}", "ERROR")
        
        except Exception as e:
            self.log(f"❌ Metrics endpoint error: {str(e)}", "ERROR")
            self.results['errors'].append(f"Metrics: {str(e)}")
        
        self.results['failed_tests'] += 1
        return False
    
    def run_all_tests(self):
        """Run the complete test suite."""
        self.log("=" * 60)
        self.log("Starting Phishing URL Detection System Tests")
        self.log("=" * 60)
        
        # Test 1: Health check
        self.test_health_check()
        
        # Test 2: Model info
        self.test_model_info()
        
        # Test 3: Single predictions
        for url in TEST_URLS[:3]:  # Test first 3 URLs
            self.test_single_prediction(url)
        
        # Test 4: Batch prediction
        self.test_batch_prediction(TEST_URLS[:2])  # Test with 2 URLs
        
        # Test 5: Invalid URL handling
        self.test_invalid_url()
        
        # Test 6: Metrics endpoint
        self.test_metrics_endpoint()
        
        # Print results
        self.print_results()
    
    def print_results(self):
        """Print test results summary."""
        self.log("=" * 60)
        self.log("TEST RESULTS SUMMARY")
        self.log("=" * 60)
        
        total = self.results['total_tests']
        passed = self.results['passed_tests']
        failed = self.results['failed_tests']
        success_rate = (passed / total * 100) if total > 0 else 0
        
        self.log(f"Total Tests: {total}")
        self.log(f"Passed: {passed}")
        self.log(f"Failed: {failed}")
        self.log(f"Success Rate: {success_rate:.1f}%")
        
        if self.results['errors']:
            self.log("\nERRORS:")
            for error in self.results['errors']:
                self.log(f"  - {error}")
        
        if failed == 0:
            self.log("\n🎉 ALL TESTS PASSED! System is working correctly.")
        else:
            self.log(f"\n⚠️  {failed} test(s) failed. Please check the errors above.")
        
        self.log("=" * 60)

def main():
    """Main test function."""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = BASE_URL
    
    print(f"Testing system at: {base_url}")
    print("Make sure the FastAPI server is running before starting tests.")
    print("Press Enter to continue or Ctrl+C to cancel...")
    
    try:
        input()
    except KeyboardInterrupt:
        print("\nTests cancelled.")
        return
    
    tester = SystemTester(base_url)
    tester.run_all_tests()

if __name__ == "__main__":
    main()
