#!/usr/bin/env python3
"""
Startup script for the Phishing URL Detection System.
This script provides a convenient way to start the server with proper configuration.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def check_requirements():
    """Check if all required files and dependencies are present."""
    print("Checking system requirements...")
    
    # Check required files
    required_files = [
        'main.py',
        'url_feature_extractor.py', 
        'data_preprocessor.py',
        'logging_config.py',
        'stacking_ensemble.joblib',
        'selected_features.csv'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing required files: {missing_files}")
        return False
    
    # Check static directory
    if not Path('static').exists():
        print("❌ Missing static directory")
        return False
    
    static_files = ['static/index.html', 'static/style.css', 'static/script.js']
    missing_static = []
    for file in static_files:
        if not Path(file).exists():
            missing_static.append(file)
    
    if missing_static:
        print(f"❌ Missing static files: {missing_static}")
        return False
    
    print("✅ All required files present")
    return True

def check_dependencies():
    """Check if required Python packages are installed."""
    print("Checking Python dependencies...")
    
    required_packages = [
        'fastapi', 'uvicorn', 'pandas', 'numpy', 'sklearn', 
        'requests', 'bs4', 'tldextract', 'lightgbm', 'xgboost',
        'category_encoders', 'pydantic', 'joblib'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            # Handle package name variations
            if package == 'sklearn':
                try:
                    __import__('sklearn')
                except ImportError:
                    missing_packages.append('scikit-learn')
            elif package == 'bs4':
                try:
                    __import__('bs4')
                except ImportError:
                    missing_packages.append('beautifulsoup4')
            else:
                missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing Python packages: {missing_packages}")
        print("Install them with: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ All dependencies installed")
    return True

def create_logs_directory():
    """Create logs directory if it doesn't exist."""
    logs_dir = Path('logs')
    if not logs_dir.exists():
        logs_dir.mkdir()
        print("📁 Created logs directory")

def start_server(host='0.0.0.0', port=8000, reload=False, log_level='info'):
    """Start the FastAPI server."""
    print(f"🚀 Starting Phishing URL Detection Server...")
    print(f"   Host: {host}")
    print(f"   Port: {port}")
    print(f"   Reload: {reload}")
    print(f"   Log Level: {log_level}")
    print()
    print(f"🌐 Web Interface: http://localhost:{port}")
    print(f"📚 API Documentation: http://localhost:{port}/docs")
    print()
    print("Press Ctrl+C to stop the server")
    print("=" * 50)
    
    try:
        import uvicorn
        uvicorn.run(
            "main:app",
            host=host,
            port=port,
            reload=reload,
            log_level=log_level
        )
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Start the Phishing URL Detection System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python start_server.py                    # Start with default settings
  python start_server.py --port 8080       # Start on port 8080
  python start_server.py --reload          # Start with auto-reload for development
  python start_server.py --host 127.0.0.1  # Start on localhost only
        """
    )
    
    parser.add_argument(
        '--host', 
        default='0.0.0.0',
        help='Host to bind the server to (default: 0.0.0.0)'
    )
    
    parser.add_argument(
        '--port', 
        type=int, 
        default=8000,
        help='Port to bind the server to (default: 8000)'
    )
    
    parser.add_argument(
        '--reload', 
        action='store_true',
        help='Enable auto-reload for development'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['critical', 'error', 'warning', 'info', 'debug'],
        default='info',
        help='Log level (default: info)'
    )
    
    parser.add_argument(
        '--skip-checks',
        action='store_true',
        help='Skip requirement checks (not recommended)'
    )
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🛡️  PHISHING URL DETECTION SYSTEM")
    print("=" * 60)
    
    if not args.skip_checks:
        # Check requirements
        if not check_requirements():
            print("\n❌ Requirement check failed. Please fix the issues above.")
            sys.exit(1)
        
        if not check_dependencies():
            print("\n❌ Dependency check failed. Please install missing packages.")
            sys.exit(1)
    
    # Create logs directory
    create_logs_directory()
    
    # Start server
    start_server(
        host=args.host,
        port=args.port,
        reload=args.reload,
        log_level=args.log_level
    )

if __name__ == "__main__":
    main()
