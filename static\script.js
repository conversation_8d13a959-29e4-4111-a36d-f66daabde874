// DOM elements
const urlInput = document.getElementById('url-input');
const analyzeBtn = document.getElementById('analyze-btn');
const loadingSection = document.getElementById('loading');
const resultSection = document.getElementById('result');
const errorSection = document.getElementById('error');
const analyzeAnotherBtn = document.getElementById('analyze-another');
const tryAgainBtn = document.getElementById('try-again');
const viewDetailsBtn = document.getElementById('view-details');
const detailsModal = document.getElementById('details-modal');
const closeModalBtn = document.getElementById('close-modal');

// State
let currentAnalysisData = null;

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Analyze button click
    analyzeBtn.addEventListener('click', analyzeURL);
    
    // Enter key in input field
    urlInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            analyzeURL();
        }
    });
    
    // Analyze another button
    analyzeAnotherBtn.addEventListener('click', resetForm);
    
    // Try again button
    tryAgainBtn.addEventListener('click', resetForm);
    
    // View details button
    viewDetailsBtn.addEventListener('click', showTechnicalDetails);
    
    // Close modal
    closeModalBtn.addEventListener('click', closeModal);
    
    // Close modal on overlay click
    document.querySelector('.modal-overlay').addEventListener('click', closeModal);
    
    // Focus on input field
    urlInput.focus();
});

// Main analysis function
async function analyzeURL() {
    const url = urlInput.value.trim();
    
    if (!url) {
        showError('Please enter a URL to analyze');
        return;
    }
    
    // Validate URL format
    if (!isValidURL(url)) {
        showError('Please enter a valid URL (e.g., https://example.com)');
        return;
    }
    
    // Show loading state
    showLoading();
    
    try {
        // Simulate loading steps
        animateLoadingSteps();
        
        // Make API request
        const response = await fetch('/predict', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ url: url })
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.detail || data.error || 'Analysis failed');
        }
        
        // Store analysis data
        currentAnalysisData = data;
        
        // Show results
        showResults(data);
        
    } catch (error) {
        console.error('Analysis error:', error);
        showError(error.message || 'Failed to analyze URL. Please try again.');
    }
}

// Show loading state with animated steps
function showLoading() {
    hideAllSections();
    loadingSection.classList.remove('hidden');
    analyzeBtn.disabled = true;
}

// Animate loading steps
function animateLoadingSteps() {
    const steps = document.querySelectorAll('.step');
    steps.forEach(step => step.classList.remove('active'));
    
    // Animate steps sequentially
    setTimeout(() => {
        document.getElementById('step-1').classList.add('active');
    }, 500);
    
    setTimeout(() => {
        document.getElementById('step-2').classList.add('active');
    }, 1500);
    
    setTimeout(() => {
        document.getElementById('step-3').classList.add('active');
    }, 2500);
}

// Show results
function showResults(data) {
    hideAllSections();
    resultSection.classList.remove('hidden');
    
    // Update result header
    const resultIcon = document.getElementById('result-icon');
    const resultTitle = document.getElementById('result-title');
    const resultUrl = document.getElementById('result-url');
    
    resultIcon.className = `result-icon ${data.prediction_label.toLowerCase()}`;
    resultTitle.textContent = `${data.prediction_label} Website`;
    resultUrl.textContent = data.url;
    
    // Update prediction label
    const predictionLabel = document.getElementById('prediction-label');
    predictionLabel.className = `prediction-label ${data.prediction_label.toLowerCase()}`;
    predictionLabel.textContent = data.prediction_label.toUpperCase();
    
    // Update confidence bar
    const confidencePercentage = Math.round(data.confidence * 100);
    document.getElementById('confidence-fill').style.width = `${confidencePercentage}%`;
    document.getElementById('confidence-value').textContent = `${confidencePercentage}%`;
    
    // Update probability bars
    const legitPercentage = Math.round(data.probabilities.legitimate * 100);
    const phishingPercentage = Math.round(data.probabilities.phishing * 100);
    
    document.getElementById('prob-legitimate').style.width = `${legitPercentage}%`;
    document.getElementById('prob-legitimate-value').textContent = `${legitPercentage}%`;
    
    document.getElementById('prob-phishing').style.width = `${phishingPercentage}%`;
    document.getElementById('prob-phishing-value').textContent = `${phishingPercentage}%`;
    
    // Update meta information
    document.getElementById('processing-time').textContent = data.processing_time;
    document.getElementById('analysis-time').textContent = formatTimestamp(data.timestamp);
    
    // Re-enable analyze button
    analyzeBtn.disabled = false;
}

// Show error
function showError(message) {
    hideAllSections();
    errorSection.classList.remove('hidden');
    document.getElementById('error-message').textContent = message;
    analyzeBtn.disabled = false;
}

// Hide all sections
function hideAllSections() {
    loadingSection.classList.add('hidden');
    resultSection.classList.add('hidden');
    errorSection.classList.add('hidden');
}

// Reset form
function resetForm() {
    hideAllSections();
    urlInput.value = '';
    urlInput.focus();
    analyzeBtn.disabled = false;
    
    // Reset loading steps
    const steps = document.querySelectorAll('.step');
    steps.forEach(step => step.classList.remove('active'));
}

// Show technical details modal
function showTechnicalDetails() {
    if (!currentAnalysisData) return;
    
    const technicalDetails = document.getElementById('technical-details');
    
    technicalDetails.innerHTML = `
        <div class="tech-section">
            <h4>Analysis Results</h4>
            <div class="tech-grid">
                <div class="tech-item">
                    <span class="tech-label">Prediction:</span>
                    <span class="tech-value">${currentAnalysisData.prediction} (${currentAnalysisData.prediction_label})</span>
                </div>
                <div class="tech-item">
                    <span class="tech-label">Confidence:</span>
                    <span class="tech-value">${(currentAnalysisData.confidence * 100).toFixed(2)}%</span>
                </div>
                <div class="tech-item">
                    <span class="tech-label">Processing Time:</span>
                    <span class="tech-value">${currentAnalysisData.processing_time}s</span>
                </div>
                <div class="tech-item">
                    <span class="tech-label">Timestamp:</span>
                    <span class="tech-value">${currentAnalysisData.timestamp}</span>
                </div>
            </div>
        </div>
        
        <div class="tech-section">
            <h4>Probability Scores</h4>
            <div class="tech-grid">
                <div class="tech-item">
                    <span class="tech-label">Legitimate:</span>
                    <span class="tech-value">${(currentAnalysisData.probabilities.legitimate * 100).toFixed(4)}%</span>
                </div>
                <div class="tech-item">
                    <span class="tech-label">Phishing:</span>
                    <span class="tech-value">${(currentAnalysisData.probabilities.phishing * 100).toFixed(4)}%</span>
                </div>
            </div>
        </div>
        
        <div class="tech-section">
            <h4>Model Information</h4>
            <div class="tech-info">
                <p>This analysis was performed using a stacking ensemble classifier trained on 30 URL features including:</p>
                <ul>
                    <li>URL structure and complexity metrics</li>
                    <li>Domain and TLD analysis</li>
                    <li>HTML content features</li>
                    <li>Security indicators (SSL, robots.txt)</li>
                    <li>Statistical measures (entropy, complexity)</li>
                </ul>
                <p>The model combines multiple machine learning algorithms for improved accuracy.</p>
            </div>
        </div>
    `;
    
    detailsModal.classList.remove('hidden');
}

// Close modal
function closeModal() {
    detailsModal.classList.add('hidden');
}

// Utility functions
function isValidURL(string) {
    try {
        // Allow URLs without protocol
        if (!string.startsWith('http://') && !string.startsWith('https://') && !string.startsWith('ftp://')) {
            if (string.includes('.') && !string.includes(' ')) {
                return true; // Likely a domain name
            }
        }
        new URL(string.startsWith('http') ? string : 'http://' + string);
        return true;
    } catch (_) {
        return false;
    }
}

function formatTimestamp(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleString();
}

// Add CSS for technical details modal
const modalStyles = `
    .modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .modal-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(5px);
    }
    
    .modal-content {
        position: relative;
        background: white;
        border-radius: 15px;
        max-width: 600px;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        z-index: 1001;
    }
    
    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 25px;
        border-bottom: 1px solid #e9ecef;
    }
    
    .modal-header h3 {
        margin: 0;
        color: #333;
    }
    
    .close-btn {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #666;
        padding: 5px;
        border-radius: 50%;
        transition: all 0.3s ease;
    }
    
    .close-btn:hover {
        background: #f8f9fa;
        color: #333;
    }
    
    .modal-body {
        padding: 25px;
    }
    
    .tech-section {
        margin-bottom: 25px;
    }
    
    .tech-section h4 {
        color: #333;
        margin-bottom: 15px;
        font-size: 1.1rem;
    }
    
    .tech-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
        margin-bottom: 15px;
    }
    
    .tech-item {
        display: flex;
        justify-content: space-between;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 6px;
    }
    
    .tech-label {
        font-weight: 600;
        color: #555;
    }
    
    .tech-value {
        color: #333;
        font-family: monospace;
    }
    
    .tech-info {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 4px solid #667eea;
    }
    
    .tech-info ul {
        margin: 10px 0;
        padding-left: 20px;
    }
    
    .tech-info li {
        margin-bottom: 5px;
    }
`;

// Inject modal styles
const styleSheet = document.createElement('style');
styleSheet.textContent = modalStyles;
document.head.appendChild(styleSheet);
