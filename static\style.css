/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.header-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-icon {
    font-size: 3rem;
    margin-bottom: 20px;
    color: #ffd700;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 700;
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
}

/* Main content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* Input section */
.input-section {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.input-container {
    max-width: 600px;
    margin: 0 auto;
}

.input-label {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.input-wrapper {
    display: flex;
    gap: 15px;
    margin-bottom: 10px;
}

.url-input {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.url-input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.analyze-btn {
    padding: 15px 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
}

.analyze-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.analyze-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.input-help {
    font-size: 0.9rem;
    color: #666;
    text-align: center;
}

/* Loading */
.loading {
    background: white;
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 20px;
}

.loading p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: #333;
}

.loading-steps {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 20px;
    border-radius: 12px;
    background: #f8f9fa;
    min-width: 120px;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.step.active {
    opacity: 1;
    background: #e3f2fd;
    color: #1976d2;
}

.step i {
    font-size: 1.5rem;
}

/* Result */
.result {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.result-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f0f0f0;
}

.result-icon {
    font-size: 3rem;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.result-icon.legitimate {
    background: linear-gradient(135deg, #4caf50, #45a049);
}

.result-icon.phishing {
    background: linear-gradient(135deg, #f44336, #d32f2f);
}

.result-title h2 {
    font-size: 1.8rem;
    margin-bottom: 5px;
}

.result-title p {
    color: #666;
    word-break: break-all;
}

.result-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.prediction-card, .details-card {
    padding: 25px;
    border-radius: 15px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
}

.prediction-label {
    text-align: center;
    padding: 20px;
    border-radius: 12px;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: white;
}

.prediction-label.legitimate {
    background: linear-gradient(135deg, #4caf50, #45a049);
}

.prediction-label.phishing {
    background: linear-gradient(135deg, #f44336, #d32f2f);
}

.confidence-bar {
    margin-top: 20px;
}

.confidence-label {
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.confidence-progress {
    height: 12px;
    background: #e9ecef;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 8px;
}

.confidence-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 6px;
    transition: width 0.8s ease;
}

.confidence-value {
    text-align: right;
    font-weight: 600;
    color: #333;
}

.details-card h3 {
    margin-bottom: 20px;
    color: #333;
}

.probability-bars {
    margin-bottom: 25px;
}

.probability-item {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.prob-label {
    min-width: 80px;
    font-weight: 600;
}

.prob-bar {
    flex: 1;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.prob-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.8s ease;
}

.prob-fill.legitimate {
    background: #4caf50;
}

.prob-fill.phishing {
    background: #f44336;
}

.prob-value {
    min-width: 40px;
    text-align: right;
    font-weight: 600;
}

.analysis-meta {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-size: 0.9rem;
}

.result-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Error */
.error {
    background: white;
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.error-icon {
    font-size: 3rem;
    color: #f44336;
    margin-bottom: 20px;
}

.error h3 {
    color: #f44336;
    margin-bottom: 15px;
}

.error p {
    color: #666;
    margin-bottom: 25px;
}

/* Footer */
.footer {
    margin-top: 40px;
    text-align: center;
    color: white;
}

.footer-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.footer-links {
    margin-top: 15px;
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.footer-links a {
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 5px;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.footer-links a:hover {
    opacity: 1;
}

/* Utility classes */
.hidden {
    display: none !important;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header-content {
        padding: 30px 20px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .input-wrapper {
        flex-direction: column;
    }
    
    .result-content {
        grid-template-columns: 1fr;
    }
    
    .result-header {
        flex-direction: column;
        text-align: center;
    }
    
    .loading-steps {
        flex-direction: column;
        align-items: center;
    }
    
    .footer-links {
        flex-direction: column;
        gap: 10px;
    }
}
